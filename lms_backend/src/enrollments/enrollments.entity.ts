import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryColumn } from 'typeorm';
import { User } from '../users/users.entity';
import { Course } from '../courses/courses.entity';

@Entity('enrollments')
export class Enrollment {
  @PrimaryColumn()
  user_id: number;

  @PrimaryColumn()
  course_id: number;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  enrollment_date: Date;

  @Column('decimal', { precision: 5, scale: 2, default: 0 })
  progress: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Course)
  @JoinColumn({ name: 'course_id' })
  course: Course;
}