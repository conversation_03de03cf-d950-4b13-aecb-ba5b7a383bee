import { <PERSON>N<PERSON><PERSON>, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCertificateDto {
  @ApiProperty({ example: 1 })
  @IsNumber()
  user_id: number;

  @ApiProperty({ example: 1 })
  @IsNumber()
  course_id: number;

  @ApiProperty({ example: 'https://certificates.example.com/cert123.pdf', required: false })
  @IsOptional()
  @IsString()
  cert_url?: string;
}