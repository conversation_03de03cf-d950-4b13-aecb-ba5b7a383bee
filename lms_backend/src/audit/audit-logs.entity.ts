import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, <PERSON>inColumn } from 'typeorm';
import { User } from '../users/users.entity';

@Entity('audit_logs')
export class AuditLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  user_id: number;

  @Column({ length: 100, nullable: true })
  action_type: string;

  @Column({ length: 100, nullable: true })
  table_name: string;

  @Column({ nullable: true })
  record_id: number;

  @Column('jsonb', { nullable: true })
  action_details: any;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  timestamp: Date;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;
}